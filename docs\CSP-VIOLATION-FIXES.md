# CSP Violation Fixes Guide

This guide helps you quickly resolve Content Security Policy (CSP) violations in your MetaPDF application.

## 🚨 Common CSP Violations

### 1. "Refused to execute inline script"

**Error Example:**
```
Refused to execute inline script because it violates the following Content Security Policy directive: "script-src 'self' 'nonce-...' https://...". Either the 'unsafe-inline' keyword, a hash ('sha256-...'), or a nonce ('nonce-...') is required to allow inline execution.
```

**Solution:**
1. Copy the SHA256 hash from the error message (format: `sha256-XXXXXXXXX`)
2. Add it to `src/middleware.ts` in the `allowedScriptHashes` array:

```typescript
const allowedScriptHashes = [
  'sha256-HASH_FROM_ERROR_MESSAGE', // Add the hash here
];
```

### 2. "Refused to connect to domain"

**Error Example:**
```
Refused to connect to 'https://region1.google-analytics.com/...' because it violates the following Content Security Policy directive: "connect-src 'self' https://...".
```

**Solution:**
1. Copy the blocked domain from the error message
2. Add it to `src/middleware.ts` in the `connect-src` array:

```typescript
'connect-src': [
  // ... existing domains
  'https://region1.google-analytics.com', // Add the blocked domain here
],
```

## ✅ Already Fixed Issues

The following CSP violations have been resolved in this update:

### ✅ Google Analytics Regional Domain
- **Added:** `https://region1.google-analytics.com` to `connect-src`
- **Added:** `https://stats.g.doubleclick.net` to `connect-src`

### ✅ SHA256 Hash Support
- **Added:** `allowedScriptHashes` array for inline script hashes
- **Added:** Infrastructure to easily add new hashes

## 🛠️ Tools Available

### CSP Helper Script

Use the provided script to generate and validate CSP hashes:

```bash
# Generate hash for inline script
node scripts/csp-helper.js hash "your inline script content"

# Validate hash format
node scripts/csp-helper.js validate "sha256-your-hash"

# Get help and guidance
node scripts/csp-helper.js guide
```

### CSP Utilities

Import utilities in your code:

```typescript
import { generateScriptHash, isValidCSPHash } from '@/lib/csp-utils';

// Generate hash for a script
const hash = generateScriptHash('console.log("Hello World");');

// Validate hash format
const isValid = isValidCSPHash('sha256-abc123...');
```

## 📍 Configuration Location

**Primary CSP Configuration:** `src/middleware.ts`

Key sections:
- **Line ~19-27:** `allowedScriptHashes` array for inline script hashes
- **Line ~56-73:** `connect-src` array for connection domains
- **Line ~35-45:** `script-src` array for script domains

## 🔄 Applying Changes

After making any CSP changes:

1. **Save the file** (`src/middleware.ts`)
2. **Restart your development server**
3. **Test in the browser** to verify violations are resolved
4. **Check browser console** for any remaining violations

## 📋 Step-by-Step Fix Process

When you encounter a CSP violation:

1. **Open browser developer tools** (F12)
2. **Go to Console tab**
3. **Find the CSP violation error** (usually in red)
4. **Copy the relevant information:**
   - For inline scripts: Copy the SHA256 hash
   - For connections: Copy the blocked domain
5. **Add to appropriate array** in `src/middleware.ts`
6. **Restart development server**
7. **Refresh browser** and verify fix

## 🎯 Specific Hashes to Add

If you're seeing specific CSP violations, add these hashes to the `allowedScriptHashes` array:

```typescript
const allowedScriptHashes = [
  // Replace with actual hashes from your CSP violation reports:
  // 'sha256-ACTUAL_HASH_FROM_VIOLATION_1',
  // 'sha256-ACTUAL_HASH_FROM_VIOLATION_2',
  // 'sha256-ACTUAL_HASH_FROM_VIOLATION_3',
];
```

## 🔍 Debugging Tips

1. **Check exact hash format:** Must be `sha256-` followed by base64 characters
2. **Verify domain format:** Must include protocol (`https://`)
3. **Case sensitivity:** Hashes and domains are case-sensitive
4. **Restart required:** Always restart dev server after CSP changes
5. **Clear cache:** Sometimes browser cache needs clearing

## 📞 Need Help?

If you're still experiencing CSP violations after following this guide:

1. Check the browser console for the exact error message
2. Use the CSP helper script to generate/validate hashes
3. Ensure you've restarted the development server
4. Verify the hash/domain was added to the correct array

## 🔐 Security Notes

- Only add hashes for scripts you trust
- Only add domains that are necessary for your application
- Regularly review and clean up unused CSP entries
- Test thoroughly after making CSP changes
