import { NextRequest, NextResponse } from 'next/server';

/**
 * Security Headers Middleware
 *
 * This middleware adds comprehensive security headers to all responses,
 * including Content Security Policy (CSP), HSTS, and other security measures.
 *
 * CSP Violation Fixes:
 * - To fix "Refused to execute inline script" errors, add the SHA256 hash
 *   from the CSP violation report to the allowedScriptHashes array
 * - To fix "Refused to connect" errors, add the domain to the connect-src array
 * - Regional Google Analytics domains are included for global compatibility
 */

// Generate a random nonce for CSP
function generateNonce(): string {
  return Buffer.from(crypto.getRandomValues(new Uint8Array(16))).toString('base64');
}

// Content Security Policy configuration
function getCSPHeader(nonce: string): string {
  const isDevelopment = process.env.NODE_ENV === 'development';

  // Common SHA256 hashes for inline scripts
  // Add the specific SHA256 hashes from your CSP violation reports here
  const allowedScriptHashes = [
    // Example format: 'sha256-HASH_VALUE_HERE'
    // Replace these with the actual hashes from your CSP violation reports:
    // 'sha256-ACTUAL_HASH_FROM_VIOLATION_REPORT_1',
    // 'sha256-ACTUAL_HASH_FROM_VIOLATION_REPORT_2',
    // 'sha256-ACTUAL_HASH_FROM_VIOLATION_REPORT_3',
  ];

  // Base CSP directives
  const cspDirectives = {
    'default-src': ["'self'"],
    'script-src': [
      "'self'",
      `'nonce-${nonce}'`,
      // Firebase and Google services
      'https://www.googletagmanager.com',
      'https://www.google-analytics.com',
      'https://apis.google.com',
      'https://www.gstatic.com',
      // Firebase specific domains
      'https://firebase.googleapis.com',
      'https://firebaseinstallations.googleapis.com',
      // Additional Google Analytics domains
      'https://googletagmanager.com',
      'https://google-analytics.com',
      // Allow specific inline script hashes
      ...allowedScriptHashes,
      // Development only
      ...(isDevelopment ? ["'unsafe-eval'", "'unsafe-inline'"] : []),
    ],
    'style-src': [
      "'self'",
      "'unsafe-inline'", // Required for Tailwind CSS and styled-components
      'https://fonts.googleapis.com',
    ],
    'font-src': [
      "'self'",
      'https://fonts.gstatic.com',
      'data:', // For base64 encoded fonts
    ],
    'img-src': [
      "'self'",
      'data:', // For base64 images
      'blob:', // For generated images
      'https:', // Allow HTTPS images
      // Specific domains
      'https://placehold.co',
      'https://www.googletagmanager.com',
      'https://www.google-analytics.com',
    ],
    'connect-src': [
      "'self'",
      // Firebase services
      'https://firebase.googleapis.com',
      'https://firebaseinstallations.googleapis.com',
      'https://firestore.googleapis.com',
      'https://identitytoolkit.googleapis.com',
      'https://securetoken.googleapis.com',
      // Google AI services
      'https://generativelanguage.googleapis.com',
      // Analytics (including regional endpoints)
      'https://www.google-analytics.com',
      'https://analytics.google.com',
      'https://region1.google-analytics.com',
      'https://stats.g.doubleclick.net',
      // WebSocket connections for development
      ...(isDevelopment ? ['ws:', 'wss:'] : []),
    ],
    'frame-src': [
      "'self'",
      // Firebase Auth
      'https://metapdf-ad495.firebaseapp.com',
    ],
    'object-src': ["'none'"],
    'base-uri': ["'self'"],
    'form-action': ["'self'"],
    'frame-ancestors': ["'none'"],
    'upgrade-insecure-requests': isDevelopment ? [] : [''],
  };

  // Convert directives object to CSP string
  return Object.entries(cspDirectives)
    .map(([directive, sources]) => {
      if (sources.length === 0) return '';
      if (sources.length === 1 && sources[0] === '') return directive;
      return `${directive} ${sources.join(' ')}`;
    })
    .filter(Boolean)
    .join('; ');
}

// Security headers configuration
function getSecurityHeaders(nonce: string) {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  return {
    // Content Security Policy
    'Content-Security-Policy': getCSPHeader(nonce),
    
    // HTTP Strict Transport Security (HSTS)
    'Strict-Transport-Security': isDevelopment 
      ? 'max-age=0' 
      : 'max-age=31536000; includeSubDomains; preload',
    
    // Prevent MIME type sniffing
    'X-Content-Type-Options': 'nosniff',
    
    // Prevent clickjacking
    'X-Frame-Options': 'DENY',
    
    // XSS Protection (legacy, but still useful)
    'X-XSS-Protection': '1; mode=block',
    
    // Referrer Policy
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    
    // Permissions Policy (formerly Feature Policy)
    'Permissions-Policy': [
      'camera=()',
      'microphone=()',
      'geolocation=()',
      'payment=()',
      'usb=()',
      'magnetometer=()',
      'accelerometer=()',
      'gyroscope=()',
    ].join(', '),
    
    // Cross-Origin policies
    'Cross-Origin-Embedder-Policy': 'credentialless',
    'Cross-Origin-Opener-Policy': 'same-origin',
    'Cross-Origin-Resource-Policy': 'same-origin',
    
    // Additional security headers
    'X-Permitted-Cross-Domain-Policies': 'none',
    'X-DNS-Prefetch-Control': 'off',
  };
}

export function middleware(request: NextRequest) {
  const response = NextResponse.next();
  const nonce = generateNonce();
  
  // Add security headers
  const securityHeaders = getSecurityHeaders(nonce);
  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value);
  });
  
  // Add nonce to response for use in components
  response.headers.set('x-nonce', nonce);
  
  // CORS headers for API routes
  if (request.nextUrl.pathname.startsWith('/api/')) {
    response.headers.set('Access-Control-Allow-Origin', process.env.NODE_ENV === 'development' ? '*' : 'https://www.metapdf.app');
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Signature');
    response.headers.set('Access-Control-Max-Age', '86400');
  }
  
  // Handle preflight requests
  if (request.method === 'OPTIONS') {
    return new Response(null, { status: 200, headers: response.headers });
  }
  
  return response;
}

// Configure which routes the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
