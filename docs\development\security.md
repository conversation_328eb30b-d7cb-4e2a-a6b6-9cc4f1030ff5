# Security Documentation

MetaPDF implements comprehensive security measures to protect user data and ensure application integrity. This document outlines all security features, configurations, and best practices.

## Security Overview

### Security Layers

1. **Transport Security** - HTTPS, HSTS, secure connections
2. **Content Security Policy** - XSS prevention, resource control
3. **Authentication & Authorization** - Firebase Auth, secure sessions
4. **Input Validation** - Data sanitization, type checking
5. **Error Handling** - Secure error messages, no information leakage
6. **Data Protection** - Encryption, secure storage

## Content Security Policy (CSP)

### Implementation

**Location**: `src/middleware.ts`

The CSP is implemented in Next.js middleware and provides comprehensive protection against XSS attacks.

### CSP Directives

```typescript
const cspDirectives = {
  'default-src': ["'self'"],
  'script-src': [
    "'self'",
    `'nonce-${nonce}'`,
    'https://www.googletagmanager.com',
    'https://www.google-analytics.com',
    'https://apis.google.com',
    'https://firebase.googleapis.com',
  ],
  'style-src': [
    "'self'",
    "'unsafe-inline'", // Required for Tailwind CSS
    'https://fonts.googleapis.com',
  ],
  'font-src': [
    "'self'",
    'https://fonts.gstatic.com',
    'data:',
  ],
  'img-src': [
    "'self'",
    'data:',
    'blob:',
    'https:',
  ],
  'connect-src': [
    "'self'",
    'https://firebase.googleapis.com',
    'https://generativelanguage.googleapis.com',
  ],
  'frame-src': [
    "'self'",
    'https://metapdf-ad495.firebaseapp.com',
  ],
  'object-src': ["'none'"],
  'base-uri': ["'self'"],
  'form-action': ["'self'"],
  'frame-ancestors': ["'none'"],
};
```

### Nonce-based CSP

Dynamic nonce generation for inline scripts:

```typescript
function generateNonce(): string {
  return Buffer.from(crypto.getRandomValues(new Uint8Array(16))).toString('base64');
}
```

### CSP Violation Fixes

#### Fixing "Refused to execute inline script" Violations

1. **Get the SHA256 hash** from the browser's CSP violation report
2. **Add the hash** to the `allowedScriptHashes` array in `src/middleware.ts`:

```typescript
const allowedScriptHashes = [
  'sha256-HASH_FROM_VIOLATION_REPORT',
  // Add more hashes as needed
];
```

3. **Restart your development server** to apply changes

#### Fixing "Refused to connect" Violations

1. **Get the blocked domain** from the CSP violation report
2. **Add the domain** to the `connect-src` array in `src/middleware.ts`:

```typescript
'connect-src': [
  // ... existing domains
  'https://region1.google-analytics.com', // Example: Regional GA endpoint
],
```

#### Using the CSP Helper Script

Generate SHA256 hashes for inline scripts:

```bash
node scripts/csp-helper.js hash "your inline script content"
```

Validate hash format:

```bash
node scripts/csp-helper.js validate "sha256-your-hash"
```

Get help and guidance:

```bash
node scripts/csp-helper.js guide
```

## Security Headers

### HTTP Security Headers

**Location**: `src/middleware.ts`

```typescript
const securityHeaders = {
  // Content Security Policy
  'Content-Security-Policy': getCSPHeader(nonce),
  
  // HTTP Strict Transport Security
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
  
  // Prevent MIME type sniffing
  'X-Content-Type-Options': 'nosniff',
  
  // Prevent clickjacking
  'X-Frame-Options': 'DENY',
  
  // XSS Protection
  'X-XSS-Protection': '1; mode=block',
  
  // Referrer Policy
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  
  // Permissions Policy
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
  
  // Cross-Origin policies
  'Cross-Origin-Embedder-Policy': 'credentialless',
  'Cross-Origin-Opener-Policy': 'same-origin',
  'Cross-Origin-Resource-Policy': 'same-origin',
};
```

### Header Explanations

- **HSTS**: Forces HTTPS connections
- **X-Content-Type-Options**: Prevents MIME sniffing attacks
- **X-Frame-Options**: Prevents clickjacking
- **X-XSS-Protection**: Browser XSS filtering
- **Referrer-Policy**: Controls referrer information
- **Permissions-Policy**: Restricts browser features
- **Cross-Origin Policies**: Isolates the application

## Authentication Security

### Firebase Authentication

**Configuration**: Secure Firebase Auth setup

```typescript
// Firebase config with security best practices
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  // ... other config
};
```

### Authentication Flow

1. **Secure Login**: Email/password with validation
2. **Session Management**: Firebase handles secure sessions
3. **Token Refresh**: Automatic token renewal
4. **Logout**: Proper session cleanup

### Security Features

- **Password Requirements**: Strong password enforcement
- **Rate Limiting**: Prevent brute force attacks
- **Session Timeout**: Automatic logout after inactivity
- **Secure Cookies**: HttpOnly, Secure, SameSite flags

## Input Validation & Sanitization

### File Upload Security

```typescript
// Smart file validation with user-friendly limits
const FILE_SIZE_LIMITS = {
  free: 100 * 1024 * 1024,  // 100MB
  pro: 200 * 1024 * 1024,   // 200MB
};

const validateFile = (file: File, planId: string): { valid: boolean; warning?: string; error?: string } => {
  // Check file type
  if (!file.type.includes('pdf') && !file.name.toLowerCase().endsWith('.pdf')) {
    return { valid: false, error: 'Only PDF files are supported.' };
  }

  // Check file size based on plan
  const maxSize = FILE_SIZE_LIMITS[planId as keyof typeof FILE_SIZE_LIMITS] || FILE_SIZE_LIMITS.free;
  if (file.size > maxSize) {
    const maxSizeMB = Math.round(maxSize / (1024 * 1024));
    const fileSizeMB = Math.round(file.size / (1024 * 1024));
    return {
      valid: false,
      error: `File is ${fileSizeMB}MB, exceeds ${maxSizeMB}MB limit for your plan.`
    };
  }

  // Warning for large files (but still allow)
  if (file.size > 50 * 1024 * 1024) {
    const fileSizeMB = Math.round(file.size / (1024 * 1024));
    return {
      valid: true,
      warning: `Large file (${fileSizeMB}MB) may take longer to process.`
    };
  }

  return { valid: true };
};
```

### Data Validation

```typescript
// Using Zod for schema validation
import { z } from 'zod';

const metadataSchema = z.object({
  title: z.string().max(200).optional(),
  author: z.string().max(100).optional(),
  subject: z.string().max(200).optional(),
  keywords: z.string().max(500).optional(),
  creator: z.string().max(100).optional(),
  producer: z.string().max(100).optional(),
});

// Validate metadata input
const validateMetadata = (data: unknown) => {
  return metadataSchema.parse(data);
};
```

### XSS Prevention

- **Input Sanitization**: All user inputs are sanitized
- **Output Encoding**: Proper encoding for display
- **CSP**: Content Security Policy prevents script injection
- **React**: Built-in XSS protection through JSX

## API Security

### Webhook Security

**Location**: `src/app/api/lemonsqueezy-webhook/route.ts`

```typescript
// Signature verification
async function verifySignature(request: NextRequest, rawBody: string): Promise<boolean> {
  const signatureHeader = request.headers.get('X-Signature');
  if (!signatureHeader) return false;
  
  const hmac = crypto.createHmac('sha256', SIGNING_SECRET);
  const digest = Buffer.from(hmac.update(rawBody).digest('hex'), 'utf8');
  const receivedSignature = Buffer.from(signatureHeader, 'utf8');
  
  return crypto.timingSafeEqual(digest, receivedSignature);
}
```

### CORS Configuration

```typescript
// CORS headers for API routes
if (request.nextUrl.pathname.startsWith('/api/')) {
  response.headers.set('Access-Control-Allow-Origin', 'https://www.metapdf.app');
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Signature');
  response.headers.set('Access-Control-Max-Age', '86400');
}
```

## Data Protection

### Encryption

- **In Transit**: TLS 1.3 for all connections
- **At Rest**: Firebase encryption for stored data
- **Client-Side**: Sensitive data not stored in localStorage

### Data Handling

```typescript
// Secure data processing
const processFileSecurely = async (file: File) => {
  try {
    // Process file in memory only
    const arrayBuffer = await file.arrayBuffer();
    const pdfDoc = await PDFDocument.load(arrayBuffer);
    
    // Extract metadata without storing file
    const metadata = extractMetadata(pdfDoc);
    
    // Clear sensitive data
    arrayBuffer.fill(0);
    
    return metadata;
  } catch (error) {
    // Secure error handling
    throw new Error('File processing failed');
  }
};
```

### Privacy Protection

- **No File Storage**: Files processed in memory only
- **Minimal Data Collection**: Only necessary data collected
- **Data Retention**: Automatic cleanup of temporary data
- **User Control**: Users control their data

## Environment Security

### Environment Variables

```bash
# Required environment variables
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_domain
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
FIREBASE_ADMIN_PRIVATE_KEY=your_private_key
LEMON_SQUEEZY_SIGNING_SECRET=your_secret
GOOGLE_AI_API_KEY=your_ai_key
```

### Security Best Practices

1. **Environment Separation**: Different configs for dev/prod
2. **Secret Management**: Secure storage of sensitive data
3. **Access Control**: Principle of least privilege
4. **Regular Updates**: Keep dependencies updated
5. **Security Audits**: Regular security reviews

## Error Handling Security

### Secure Error Messages

```typescript
// Don't expose sensitive information
const getSecureErrorMessage = (error: Error): string => {
  // Log full error internally
  console.error('[Security] Error occurred:', error);
  
  // Return safe message to user
  if (error.message.includes('authentication')) {
    return 'Authentication failed. Please try again.';
  }
  
  if (error.message.includes('network')) {
    return 'Network error. Please check your connection.';
  }
  
  return 'An error occurred. Please try again.';
};
```

### Information Disclosure Prevention

- **Generic Error Messages**: No sensitive info in user-facing errors
- **Error Logging**: Detailed errors logged securely
- **Stack Traces**: Hidden in production
- **Debug Info**: Only available in development

## Security Monitoring

### Logging Strategy

```typescript
// Security event logging
const logSecurityEvent = (event: string, details: any) => {
  const securityLog = {
    timestamp: new Date().toISOString(),
    event,
    details: sanitizeLogData(details),
    userAgent: request.headers.get('user-agent'),
    ip: getClientIP(request),
  };
  
  // Send to security monitoring service
  console.log('[SECURITY]', securityLog);
};
```

### Monitoring Points

- **Authentication Events**: Login attempts, failures
- **File Upload Events**: Large files, suspicious patterns
- **API Usage**: Rate limiting, unusual patterns
- **Error Patterns**: Repeated errors, potential attacks

## Compliance & Standards

### Security Standards

- **OWASP Top 10**: Protection against common vulnerabilities
- **GDPR**: Data protection compliance
- **SOC 2**: Security controls implementation
- **ISO 27001**: Information security management

### Regular Security Tasks

1. **Dependency Updates**: Weekly security updates
2. **Vulnerability Scanning**: Automated security scans
3. **Penetration Testing**: Quarterly security assessments
4. **Security Reviews**: Code security reviews
5. **Incident Response**: Security incident procedures

## Security Checklist

### Development Security

- [ ] Input validation implemented
- [ ] Output encoding applied
- [ ] Authentication properly configured
- [ ] Authorization checks in place
- [ ] Error handling secure
- [ ] Dependencies updated
- [ ] Security headers configured
- [ ] CSP properly implemented

### Deployment Security

- [ ] HTTPS enforced
- [ ] Environment variables secured
- [ ] Database access restricted
- [ ] Monitoring configured
- [ ] Backup procedures tested
- [ ] Incident response plan ready

---

This security documentation ensures MetaPDF maintains the highest security standards while providing a seamless user experience.
