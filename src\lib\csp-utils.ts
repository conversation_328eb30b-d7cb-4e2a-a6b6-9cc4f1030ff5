/**
 * CSP (Content Security Policy) Utilities
 * 
 * This file contains utilities for managing CSP configurations and handling violations.
 */

import crypto from 'crypto';

/**
 * Generate SHA256 hash for inline script content
 * Use this to generate hashes for inline scripts that need to be allowed in CSP
 * 
 * @param scriptContent - The exact content of the inline script
 * @returns SHA256 hash in the format expected by CSP (sha256-HASH)
 */
export function generateScriptHash(scriptContent: string): string {
  const hash = crypto.createHash('sha256').update(scriptContent, 'utf8').digest('base64');
  return `sha256-${hash}`;
}

/**
 * Common inline script patterns that might need CSP allowances
 * These are typical patterns found in web applications
 */
export const COMMON_SCRIPT_PATTERNS = {
  // Google Analytics gtag initialization
  GTAG_INIT: `window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}
gtag('js', new Date());`,

  // Firebase Analytics initialization
  FIREBASE_ANALYTICS: `import { analytics } from '@/lib/firebase';`,

  // Common error tracking patterns
  ERROR_TRACKING: `window.addEventListener('error', function(e) {
  console.error('Global error:', e);
});`,
};

/**
 * Generate hashes for common script patterns
 * Use this to pre-generate hashes for known script patterns
 */
export function generateCommonHashes(): Record<string, string> {
  const hashes: Record<string, string> = {};
  
  Object.entries(COMMON_SCRIPT_PATTERNS).forEach(([key, script]) => {
    hashes[key] = generateScriptHash(script);
  });
  
  return hashes;
}

/**
 * Instructions for fixing CSP violations
 */
export const CSP_VIOLATION_GUIDE = {
  INLINE_SCRIPT: `
To fix "Refused to execute inline script" violations:
1. Copy the SHA256 hash from the browser's CSP violation report
2. Add it to the allowedScriptHashes array in src/middleware.ts
3. Format: 'sha256-HASH_VALUE_FROM_VIOLATION_REPORT'
4. Restart your development server to apply changes
`,

  CONNECT_SRC: `
To fix "Refused to connect" violations:
1. Copy the blocked domain from the browser's CSP violation report
2. Add it to the connect-src array in src/middleware.ts
3. Format: 'https://domain.com'
4. Restart your development server to apply changes
`,

  SCRIPT_SRC: `
To fix "Refused to load script" violations:
1. Copy the blocked domain from the browser's CSP violation report
2. Add it to the script-src array in src/middleware.ts
3. Format: 'https://domain.com'
4. Restart your development server to apply changes
`,
};

/**
 * Validate CSP hash format
 * @param hash - The hash to validate
 * @returns true if the hash is in the correct format
 */
export function isValidCSPHash(hash: string): boolean {
  return /^sha256-[A-Za-z0-9+/]+=*$/.test(hash);
}

/**
 * Extract hash from CSP violation report
 * @param violationMessage - The violation message from the browser
 * @returns The extracted hash or null if not found
 */
export function extractHashFromViolation(violationMessage: string): string | null {
  const hashMatch = violationMessage.match(/sha256-([A-Za-z0-9+/]+=*)/);
  return hashMatch ? `sha256-${hashMatch[1]}` : null;
}

/**
 * Common Google Analytics and Firebase domains that might be needed
 */
export const ANALYTICS_DOMAINS = {
  GOOGLE_ANALYTICS: [
    'https://www.google-analytics.com',
    'https://analytics.google.com',
    'https://region1.google-analytics.com',
    'https://stats.g.doubleclick.net',
    'https://www.googletagmanager.com',
    'https://googletagmanager.com',
  ],
  FIREBASE: [
    'https://firebase.googleapis.com',
    'https://firebaseinstallations.googleapis.com',
    'https://firestore.googleapis.com',
    'https://identitytoolkit.googleapis.com',
    'https://securetoken.googleapis.com',
  ],
  GOOGLE_APIS: [
    'https://apis.google.com',
    'https://www.googleapis.com',
    'https://generativelanguage.googleapis.com',
  ],
};
