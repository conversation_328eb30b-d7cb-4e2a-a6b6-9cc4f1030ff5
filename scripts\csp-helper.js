#!/usr/bin/env node

/**
 * CSP Helper Script
 * 
 * This script helps you manage Content Security Policy violations by:
 * 1. Generating SHA256 hashes for inline scripts
 * 2. Providing guidance on fixing CSP violations
 * 3. Validating CSP hash formats
 * 
 * Usage:
 *   node scripts/csp-helper.js hash "your inline script content"
 *   node scripts/csp-helper.js validate "sha256-your-hash"
 *   node scripts/csp-helper.js guide
 */

const crypto = require('crypto');

function generateScriptHash(scriptContent) {
  const hash = crypto.createHash('sha256').update(scriptContent, 'utf8').digest('base64');
  return `sha256-${hash}`;
}

function isValidCSPHash(hash) {
  return /^sha256-[A-Za-z0-9+/]+=*$/.test(hash);
}

function showGuide() {
  console.log(`
🔒 CSP Violation Fix Guide
========================

📋 Common CSP Violations and Solutions:

1. "Refused to execute inline script because it violates CSP"
   ✅ Solution: Add the SHA256 hash to allowedScriptHashes in src/middleware.ts
   📝 Format: 'sha256-HASH_FROM_VIOLATION_REPORT'

2. "Refused to connect to 'https://domain.com' because it violates CSP"
   ✅ Solution: Add the domain to connect-src in src/middleware.ts
   📝 Format: 'https://domain.com'

3. "Refused to load the script 'https://domain.com/script.js'"
   ✅ Solution: Add the domain to script-src in src/middleware.ts
   📝 Format: 'https://domain.com'

🛠️ Using this script:
   Generate hash: node scripts/csp-helper.js hash "your script content"
   Validate hash: node scripts/csp-helper.js validate "sha256-your-hash"

📍 CSP Configuration Location: src/middleware.ts (lines ~19-27 for hashes)

🔄 After making changes, restart your development server!
`);
}

function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  switch (command) {
    case 'hash':
      if (!args[1]) {
        console.error('❌ Error: Please provide script content to hash');
        console.log('Usage: node scripts/csp-helper.js hash "your script content"');
        process.exit(1);
      }
      const hash = generateScriptHash(args[1]);
      console.log(`✅ Generated CSP hash: ${hash}`);
      console.log(`📝 Add this to allowedScriptHashes in src/middleware.ts:`);
      console.log(`   '${hash}',`);
      break;

    case 'validate':
      if (!args[1]) {
        console.error('❌ Error: Please provide a hash to validate');
        console.log('Usage: node scripts/csp-helper.js validate "sha256-your-hash"');
        process.exit(1);
      }
      const isValid = isValidCSPHash(args[1]);
      if (isValid) {
        console.log(`✅ Valid CSP hash format: ${args[1]}`);
      } else {
        console.log(`❌ Invalid CSP hash format: ${args[1]}`);
        console.log('Expected format: sha256-BASE64_HASH');
      }
      break;

    case 'guide':
    case 'help':
    case undefined:
      showGuide();
      break;

    default:
      console.error(`❌ Unknown command: ${command}`);
      console.log('Available commands: hash, validate, guide, help');
      process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  generateScriptHash,
  isValidCSPHash,
  showGuide
};
